#!/usr/bin/env python3
"""
测试神经分类器聚合功能的修复
"""

import numpy as np
from pyiaf.neural_classifier import NeuralClassifier, aggregate_classifiers

def test_classifier_aggregation():
    """测试分类器聚合功能"""
    print("测试神经分类器聚合功能...")
    
    # 创建不同大小的训练数据
    np.random.seed(42)
    
    # 客户端1：较小的输入维度
    X1 = np.random.randn(100, 20)  # 10维问题的成对数据
    y1 = np.random.choice([-1, 0, 1], 100)
    
    # 客户端2：较大的输入维度
    X2 = np.random.randn(100, 22)  # 11维问题的成对数据
    y2 = np.random.choice([-1, 0, 1], 100)
    
    # 客户端3：与客户端1相同的维度
    X3 = np.random.randn(100, 20)  # 10维问题的成对数据
    y3 = np.random.choice([-1, 0, 1], 100)
    
    # 创建分类器
    classifiers = {}
    
    # 训练客户端1的分类器
    print("训练客户端1的分类器...")
    classifier1 = NeuralClassifier(random_state=42)
    classifier1.fit(X1, y1)
    classifiers[0] = classifier1
    print(f"客户端1权重大小: {len(classifier1.get_weights())}")
    
    # 训练客户端2的分类器
    print("训练客户端2的分类器...")
    classifier2 = NeuralClassifier(random_state=43)
    classifier2.fit(X2, y2)
    classifiers[1] = classifier2
    print(f"客户端2权重大小: {len(classifier2.get_weights())}")
    
    # 训练客户端3的分类器
    print("训练客户端3的分类器...")
    classifier3 = NeuralClassifier(random_state=44)
    classifier3.fit(X3, y3)
    classifiers[2] = classifier3
    print(f"客户端3权重大小: {len(classifier3.get_weights())}")
    
    # 测试聚合
    print("\n测试聚合功能...")
    try:
        agg_classifier = aggregate_classifiers(classifiers, [0, 1, 2])
        print("✓ 聚合成功！")
        print(f"聚合后分类器权重大小: {len(agg_classifier.get_weights())}")
        
        # 测试预测
        test_X = np.random.randn(10, agg_classifier.input_dim)
        predictions = agg_classifier.predict(test_X)
        print(f"✓ 预测成功！预测结果形状: {predictions.shape}")
        
    except Exception as e:
        print(f"✗ 聚合失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n所有测试通过！")
    return True

if __name__ == "__main__":
    test_classifier_aggregation()
