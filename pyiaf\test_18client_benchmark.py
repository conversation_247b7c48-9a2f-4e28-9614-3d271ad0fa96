#!/usr/bin/env python3
"""
基准测试脚本：18客户端，10维，110次评估（50初始+60迭代），Non-IID分区，1轮
"""

import numpy as np
import pandas as pd
import time
import os
from pyiaf import IAF_FBO, create_partitions
from pyiaf.Tasks.benchmark import create_tasks_diff_func

def run_benchmark_test():
    """运行18客户端基准测试"""
    
    # 测试参数
    n_clients = 18
    dimension = 10
    n_initial = 50
    max_iterations = 60
    total_evaluations = n_initial + max_iterations  # 110次评估
    
    print(f"开始基准测试:")
    print(f"- 客户端数量: {n_clients}")
    print(f"- 问题维度: {dimension}")
    print(f"- 初始采样: {n_initial}")
    print(f"- 最大迭代: {max_iterations}")
    print(f"- 总评估次数: {total_evaluations}")
    print(f"- 分区策略: Non-IID (np_per_dim=2)")
    print("-" * 50)
    
    # 创建18个不同的任务函数
    tasks = create_tasks_diff_func(dim=dimension, normalized=False)
    print(f"创建了 {len(tasks)} 个基准任务")
    
    # 为每个任务设置边界
    task_bounds = []
    for i, task in enumerate(tasks):
        if hasattr(task, 'x_lb') and hasattr(task, 'x_ub'):
            lower = np.full(dimension, task.x_lb)
            upper = np.full(dimension, task.x_ub)
            task_bounds.append((lower, upper))
        else:
            # 默认边界
            lower = np.full(dimension, -5.0)
            upper = np.full(dimension, 5.0)
            task_bounds.append((lower, upper))
    
    print(f"任务边界设置完成")
    
    # 创建Non-IID分区 (np_per_dim=2)
    global_bounds = (np.full(dimension, -100.0), np.full(dimension, 100.0))
    client_bounds = create_partitions(
        strategy='non_iid',
        bounds=global_bounds,
        n_clients=n_clients,
        np_per_dim=2,
        overlap_ratio=0.1,
        random_state=42
    )
    
    print(f"Non-IID分区创建完成，重叠比例: 0.1")
    
    # 创建IAF-FBO优化器
    optimizer = IAF_FBO(
        n_clients=n_clients,
        bounds=client_bounds,  # 使用Non-IID分区边界
        n_initial=n_initial,
        max_iterations=max_iterations,
        af_type='LCB',
        n_clusters=6,
        pop_size=100,
        cso_iters=100,
        transfer_prob=0.5,
        noise_prob=0.0,
        random_state=42
    )
    
    print(f"IAF-FBO优化器创建完成")
    
    # 设置目标函数
    objective_functions = {}
    for client_id in range(n_clients):
        task = tasks[client_id]
        objective_functions[client_id] = lambda x, t=task: t(x)
    
    print(f"目标函数设置完成")
    
    # 运行优化
    print("开始优化...")
    start_time = time.time()
    
    optimizer.setup_clients(objective_functions)
    results = optimizer.run_optimization()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"优化完成，总耗时: {total_time:.2f}秒")
    print("-" * 50)
    
    # 输出结果
    print("优化结果:")
    best_values = []
    for client_id in range(n_clients):
        best_val = results['best_values'][client_id]
        best_sol = results['best_solutions'][client_id]
        best_values.append(best_val)
        print(f"客户端 {client_id:2d}: 最优值 = {best_val:10.6f}")
    
    print("-" * 50)
    print(f"统计信息:")
    print(f"- 平均最优值: {np.mean(best_values):.6f}")
    print(f"- 最优值标准差: {np.std(best_values):.6f}")
    print(f"- 最好结果: {np.min(best_values):.6f}")
    print(f"- 最差结果: {np.max(best_values):.6f}")
    
    # 保存结果到CSV文件
    results_data = []
    for client_id in range(n_clients):
        # 获取每个客户端的评估历史
        eval_history = optimizer.history['evaluation_history'][client_id]
        eval_times = optimizer.history['evaluation_times'][client_id]
        
        for eval_idx, (value, eval_time) in enumerate(zip(eval_history, eval_times)):
            results_data.append({
                'client_id': client_id,
                'evaluation': eval_idx + 1,
                'value': value,
                'time': eval_time,
                'is_best': value == results['best_values'][client_id]
            })
    
    # 创建DataFrame并保存
    df = pd.DataFrame(results_data)
    filename = f"benchmark_results_18clients_10d_110evals_niid2.csv"
    df.to_csv(filename, index=False)
    print(f"结果已保存到: {filename}")
    
    # 保存每个客户端的110次真实评估值
    client_results = []
    for client_id in range(n_clients):
        eval_history = optimizer.history['evaluation_history'][client_id]
        # 确保每个客户端有110次评估
        if len(eval_history) >= 110:
            client_110_values = eval_history[:110]
        else:
            # 如果不足110次，用最后的值填充
            client_110_values = eval_history + [eval_history[-1]] * (110 - len(eval_history))
        
        client_results.append(client_110_values)
    
    # 转换为DataFrame，每行是一个客户端的110次评估
    client_df = pd.DataFrame(client_results)
    client_df.index.name = 'client_id'
    
    # 保存客户端评估结果
    client_filename = f"client_evaluations_18clients_10d_110evals_niid2.csv"
    client_df.to_csv(client_filename)
    print(f"客户端评估历史已保存到: {client_filename}")
    
    return results, total_time

if __name__ == "__main__":
    try:
        results, runtime = run_benchmark_test()
        print(f"\n测试成功完成！总运行时间: {runtime:.2f}秒")
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
